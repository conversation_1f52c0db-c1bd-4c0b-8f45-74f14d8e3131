<template>
	
	<movable-area class="u-component u-drag " :style="listStyle">
		<movable-view v-for="(item, index) in newList" :key="item[field]" direction="all" :damping="40" inertia
			class="u-drag-item " :class="{ 'u-drag-item--active': enable && index === moveIndex }" :style="itemStyle"
			:x="item.x" :y="item.y" :disabled="!enable || item.disabled" @longpress="!item.disabled && setEnable(true)"
			@change="handleChange($event, index)" @touchstart="handleTouchstart(index)" @touchend="handleTouchend()">
			<slot :item="item" :index="index"></slot>
		</movable-view>
	</movable-area>
</template>

<script setup>
	import {
		ref,
		computed,
		nextTick,
		watch
	} from 'vue'
	import pickBy from 'lodash/pickBy'
	import debounce from 'lodash/debounce'
	import sortBy from 'lodash/sortBy'
	import useState from './tools/useState'

	const $emit = defineEmits(['update'])

	const props = defineProps({
		list: {
			type: Array,
			default () {
				return []
			},
		},
		field: {
			type: String,
			default: "CID", // 此字段非常重要，必须要唯一的ID，不然无法正常排序
		},
		column: {
			type: Number,
			default: 1,
		},
		itemHeight: {
			type: Number,
			default: 160,
		},
		listWidth: {
			type: Number,
			default: 700, //380*2
		},
		itemInnerStyle: {
			type: Object,
			default () {
				return {}
			},
		},
	});
	// props end

	const [enable, setEnable] = useState(false)
	const [moveIndex, setMoveIndex] = useState(0)
	const [moveSortIndex, setMoveSortIndex] = useState(0)
	const [moveToSortIndex, setMoveToSortIndex] = useState(0)
	const newList = ref(props.list)

	const listStyle = computed(() =>
		pickBy({
			width: props.listWidth + 'rpx',
			height: props.itemHeight * Math.ceil(props.list.length / props.column) + 'rpx'
		})
	)
	const itemStyle = computed(() =>
		pickBy({
			...props.itemInnerStyle,
			width: props.listWidth / props.column + 'rpx',
			height: props.itemHeight + 'rpx'
		})
	)

	// index: number
	const getX = (index) => {
		return (index % props.column) * (props.listWidth / props.column)
	}
	//index: number
	const getY = (index) => {
		return Math.floor(index / props.column) * props.itemHeight
	}

	const initList = () => {
		newList.value.map(item => {
			item.x = getX(item.sortIndex) + 'rpx'
			item.y = getY(item.sortIndex) + 'rpx'
		})
	}
	//index: number
	const handleTouchstart = (index) => {
		//debugger
		console.log('handleTouchstart：',index)
		setMoveIndex(index)
		setMoveSortIndex(newList.value[index].sortIndex)
	}

	//function (e: any, index: number
	const handleChange = debounce(function(e, index) {
		if (e.detail.source === 'touch') {
			setMoveIndex(index)
			setMoveSortIndex(newList.value[index].sortIndex)

			newList.value[index].x = e.detail.x
			newList.value[index].y = e.detail.y

			const itemWidth = props.listWidth / props.column

			const maxArea = ((itemWidth / 2) * (props.itemHeight / 2)) / 2

			for (let i = 0; i < newList.value.length; i++) {
				const item = newList.value[i]
				const targetX = getX(item.sortIndex)
				const targetY = getY(item.sortIndex)
				if (
					Math.abs(targetX / 2 - e.detail.x) > itemWidth / 2 ||
					Math.abs(targetY / 2 - e.detail.y) > props.itemHeight / 2 ||
					item.disabled
				) {
					continue
				} else if (
					(itemWidth / 2 - Math.abs(targetX / 2 - e.detail.x)) *
					(props.itemHeight / 2 - Math.abs(targetY / 2 - e.detail.y)) >
					maxArea
				) {
					// 两个item 相交面积大于item面积的一半
					setMoveToSortIndex(item.sortIndex)
					console.log('toSortIndex', item.sortIndex)

					if (moveSortIndex.value > moveToSortIndex.value) {
						newList.value.map(item => {
							if (item.sortIndex === moveSortIndex.value) {
								item.sortIndex = moveToSortIndex.value
							} else if (
								item.sortIndex >= moveToSortIndex.value &&
								item.sortIndex < moveSortIndex.value
							) {
								item.sortIndex++
								item.x = getX(item.sortIndex) + 'rpx'
								item.y = getY(item.sortIndex) + 'rpx'
							}
						})
					} else {
						newList.value.map(item => {
							if (item.sortIndex === moveSortIndex.value) {
								item.sortIndex = moveToSortIndex.value
							} else if (
								item.sortIndex <= moveToSortIndex.value &&
								item.sortIndex > moveSortIndex.value
							) {
								item.sortIndex--
								item.x = getX(item.sortIndex) + 'rpx'
								item.y = getY(item.sortIndex) + 'rpx'
							}
						})
					}

					break
				}
			}
		}
	}, 80)

	const handleTouchend = () => {
		if (enable.value) {
			console.log('=====handleTouchend====')
			nextTick(() => {
				initList()
			})
			
			// 根据实际需要，此处可自定义
			let newData = [...newList.value].sort(function(a, b) {
				    //console.log('handleTouchend a and b:',a,b)
					return a.sortIndex - b.sortIndex
				})
				//debugger
			$emit('update',newData)
			setEnable(false)
		}
	}

	watch(
		() => props.list,
		() => {
			newList.value = props.list.map((item, index) => {
				return {
					...item,
					index,
					sortIndex: index
				}
			})

			initList()
		}, {
			immediate: true,
			deep: true
		}
	)
</script>

<style lang="scss">
	.u-drag {
		position: relative;

		&-item {
			&--active {
				z-index: 9;
				background: #e9e9e9;
				border: 1px dashed #ccc;
				box-shadow: 0 4rpx 16rpx 4rpx #ccc;
			}
		}
	}
</style>