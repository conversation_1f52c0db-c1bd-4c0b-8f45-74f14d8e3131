/**
 * @version 1.0.0
 * <AUTHOR> 2024-11-28
 * 参考文档: https://www.quanzhan.co/luch-request/
 */
// 基于 Promise 对象实现更简单的 request 使用方式，支持请求和响应拦截 
// • 支持全局挂载 
// • 支持多个全局配置实例 
// • 支持自定义验证器 
// • 支持文件上传/下载 
// • 支持task 操作 
// • 支持自定义参数 
// • 支持多拦截器 
// • 对参数的处理比uni.request 更强
import * as serverConfig from '@/config/index'
import { setStorageSync, getStorageSync, CURRENT_SERVER } from '@/utils/Storage.js'
import Request from '@/js_sdk/luch-request/luch-request/index.js'

// 获取设备 deviceId
const getDEVICE_INFO = () => {
	let _DEVICE_INFO = {}
	try {
		_DEVICE_INFO = getStorageSync('DEVICE_INFO')
	} catch (e) {}
	return _DEVICE_INFO
}
const getTokenStorage = () => {
	let token = ''
	try {
		token = uni.getStorageSync('token')
	} catch (e) {}
	return token
}

const getUserInfo = () => {
	let _userInfo = {}
	try {
		_userInfo = getStorageSync('USER_INFO')
	} catch (e) {}
	return _userInfo
}

const multiOrganization = () => {
	let _organization = {
		CORG_CODE:'',
		CORG_NAME:''
	}
	try {
		 _organization = getStorageSync('USER_INFO').Orgs[0]
		if(getStorageSync('USER_INFO').Orgs.length>0){
			//element.CIS_DEFAULT=='Y'&&!this.multiOrganization.CORG_NAME
			getStorageSync('USER_INFO').Orgs.find(item => {
				if(item.CIS_DEFAULT=='Y'&&!_organization.CORG_NAME){
					_organization = item
				}
			})
		}

	} catch (e) {}
	return _organization
}

const axios = new Request()
/**
 * 修改全局配置示例
 const axios = new Request({
	header: {a:1}, // 举例
	baseURL: 'https://www.fastmock.site/mock/26243bdf9062eeae2848fc67603bda2d/luchrequest',
	validateStatus: (statusCode) => { // statusCode 必存在。此处示例为全局默认配置
		return statusCode >= 200 && statusCode < 300
	}
})
 axios.config.baseURL = 'https://www.fastmock.site/mock/26243bdf9062eeae2848fc67603bda2d/luchrequest'
 **/
console.log(`axios 请求实例的插件版本为：${axios.version}`)

//ANDY:getUserInfo()
// console.log(` getUserInfo:${getUserInfo()}`)
const _baseURL = (process.env.NODE_ENV === 'production'?serverConfig.baseUrl.pro:serverConfig.baseUrl.dev)
const getServerBaseURL= () => {
	//debugger
	let _url = _baseURL
	try {
		let data = getStorageSync(CURRENT_SERVER)
		if (data && data.CMIDDLEWARE) {
			//"CMIDDLEWARE": "http://192.168.1.67:6682/api/"
			_url = data.CMIDDLEWARE
			if(!_url.endsWith('/')){
				_url += '/'
			}
		}
	} catch (e) {
		_url = _baseURL
	}
	return _url
}
axios.setConfig((config) => {
	/* 设置全局配置 */
	config.baseURL = _baseURL
	config.header = {
		...config.header,
		// a: 1, // 演示
		// b: 2 // 演示
	}
	config.custom = {
		// auth: false, // 是否传token
		// loading: false // 是否使用loading
	}
	return config
})

axios.interceptors.request.use((config) => {
	config.baseURL = getServerBaseURL()
	/* 请求之前拦截器。可以使用async await 做异步操作  */
	config.header = {
		...config.header,
		InstanceID:getDEVICE_INFO().deviceId,//设备id
		CSERIAL:'',// 序号,编号,后端返回
		systemtype:'APP',//来源系统
		sourceType:'plugin',//来源类型
		MD5:'',// 子应用唯一ID,比较更新
		token: getTokenStorage(),
	    'Accept':"currentusername$$"+getUserInfo()?.CUSER_NAME+"_-enterprisecode$$"+getUserInfo()?.CENTERPRISE_CODE+'_-OrgCode$$'+multiOrganization()?.CORG_CODE,
		//a: 3 // 演示
	}
	/**
	 * custom {Object} - 自定义参数
	 */
	// if (config.custom.auth) {
	//   config.header.token = '123456'
	// }
	// if (config.custom.loading) {
	//   uni.showLoading()
	// }
	/*
	if (!token) { // 如果token不存在，return Promise.reject(config) 会取消本次请求
	  return Promise.reject(config)
	}
	*/
	return config
}, (config) => {
	return Promise.reject(config)
})


axios.interceptors.response.use((response) => {
	/* 请求之后拦截器。可以使用async await 做异步操作  */
	// if (response.config.custom.loading) {
	//    uni.hideLoading()
	//  }
	if (response.data.code !== 200) { // 服务端返回的状态码不等于200，则reject()
		
			// 处理 HTTP 网络错误
		let message = "";
		// HTTP 状态码
		const status = response.data.code;	
		   switch (status) {
		          case 400:
		            message = "请求错误";
		            break;
		          case 401:
		            message = "未授权，请登录";
		            break;
		          case 403:
		            message = "拒绝访问";
		            break;
		          case 404:
		            message = `请求地址出错:xxxx `;//${error.response?.config?.url}
		            break;
		          case 408:
		            message = "请求超时";
		            break;
		          case 500:
		            message = "服务器内部错误";
		            break;
		          case 501:
		            message = "服务未实现";
		            break;
		          case 502:
		            message = "网关错误";
		            break;
		          case 503:
		            message = "服务不可用";
		            break;
		          case 504:
		            message = "网关超时";
		            break;
		          case 505:
		            message = "HTTP版本不受支持";
		            break;
		          default:
		            message = "网络连接故障";
		        }
		
				  uni.showLoading({
					 title: message
				  });
				  setTimeout(function () {
				  	uni.hideLoading();
				  }, 2000);
				return Promise.reject(response)
	}

	return response
}, (response) => {
	// 请求错误做点什么。可以使用async await 做异步操作
	// if (response.config.custom.loading) {
	//    uni.hideLoading()
	//  }
	return Promise.reject(response)
})


const http = new Request()
http.setConfig((config) => {
	/* 设置全局配置 */
	config.baseURL = _baseURL//'https://www.fastmock.site/mock/26243bdf9062eeae2848fc67603bda2d/luchrequest' /* 根域名不同 */
	config.header = {
		...config.header,
		// a: 1, // 演示
		// b: 2 // 演示
	}
	return config
})


http.interceptors.request.use((config) => {
	/* 请求之前拦截器。可以使用async await 做异步操作 */
	config.header = {
		...config.header,
		//token: getTokenStorage()
	}
	/*
 if (!token) { // 如果token不存在，return Promise.reject(config) 会取消本次请求
   return Promise.reject(config)
 }
 */
	return config
}, (config) => {
	return Promise.reject(config)
})


http.interceptors.response.use(async (response) => {
	/* 请求之后拦截器。可以使用async await 做异步操作  */
	// if (response.data.code !== 200) { // 服务端返回的状态码不等于200，则reject()
	//   return Promise.reject(response)
	// }
	return response
}, (response) => { // 请求错误做点什么。可以使用async await 做异步操作
	console.log(response)
	return Promise.reject(response)
})

export {
	http,
	axios
}