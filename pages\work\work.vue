<template>
	<view class="pageCustom">
		<uni-nav-bar status-bar :fixed="true" :border="false">
			<view class="flex fontSize-34  w-full  justify-center items-center">工作台</view>
			<block v-slot:right>
				<uni-icons @click="toggleMenu()" type="more-filled" size="30"></uni-icons>
			</block>
		</uni-nav-bar>
		
		<view :style="{ height: `${windowBodyHeight}px` }" class="flex bg-white w-full">
			<view class="flex justify-between  w-full flex-col">
				<!-- 分类 -->
				<u-sticky style="background-color: #efeff4;" class="p-all-20  " offsetTop="0">
					<uni-easyinput prefixIcon="search" @change="searchAppClick" @clear="searchAppClick" v-model="searchInput" placeholder="搜索应用" @iconClick="searchAppClick"></uni-easyinput>
						
				</u-sticky>
				<!-- 列表内容 menuCategory.Children-->
				<scroll-view scroll-y="true" class="flex-1 overflow-y-auto">
					<view class=" flex  flex-col pl-20 pr-20 ">
						<uni-section title="收藏应用 (长按可调整位置)" type="line">

							<com-drag :list="list" :listWidth="listWidth" :column="4" field="CID"
								:itemHeight="itemHeight" @update="setList">
								<template #default="{ item }">
									<view @click="navigateTo('/pages/common/common', item)" :style="{ height: `${itemHeight}rpx` }"
										class="flex  relative justify-center flex-col items-center ">
										<view class="flex">
											<image :src="item.url || '/static/images/appICON.png'" class="imageItem"
												mode="aspectFill" />
										</view>
										<view class="flex">
											<text class="textDesc">{{ item.CMODULE_NAME }}</text>
										</view>

										<view @click.stop="removeItem(item)" v-show="isEdit" class="absolute top-0 right-0">
											<uni-icons type="minus-filled" color="red" size="28"></uni-icons>
										</view>
									</view>
								</template>
							</com-drag>
						</uni-section>

					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script setup>
import * as serviceApi from '@/api/index.js'
import { getCurrentUserInfoByField } from '@/utils/tools.js'
import {
	setStorageSync,
	getStorageSync,
	CURRENT_SERVER
} from '@/utils/Storage.js'
import comDrag from '@/components/com-drag/com-drag.vue'
import { onPullDownRefresh } from '@dcloudio/uni-app';
//运用 position:sticky或position: fixed 可实现头部导航栏固定(吸顶效果)
import {
	ref,
	computed,
	onMounted
} from 'vue'
const searchInput = ref('')
const navBarHeight = ref(44) // 顶部标题高度
const tabBarHeight = ref(50) // 底部菜单栏高度	
const menuCategory = ref([])
const systemInfo = uni.getSystemInfoSync(); //系统信息
const windowHeight = computed(() => {
	//windowHeight不包含NavigationBar和TabBar的高度
	return systemInfo.windowHeight
})
const windowBodyHeight = computed(() => {
	//windowHeight不包含NavigationBar和TabBar的高度
	return systemInfo.windowHeight - navBarHeight.value - systemInfo.safeArea.top
})
const isEdit = ref(false)
const listWidth = ref(730)
const itemHeight = ref(160)
const copyList = ref([])
const list = ref(
	[
		// {
		// 	id: 1,
		// 	url: '/static/images/appICON.png',
		// 	text: '设备巡检保养',
		// 	badge: '0',
		// 	type: "primary"
		// },
		// {
		// 	id: 2,
		// 	url: '/static/images/appICON.png',
		// 	text: '未保养任务',
		// 	badge: '1',
		// 	type: "success"
		// },
		// {
		// 	id: 3,
		// 	url: '/static/images/appICON.png',
		// 	text: '超时未保养',
		// 	badge: '99',
		// 	type: "warning"
		// },
		// {
		// 	id: 4,
		// 	url: '/static/images/appICON.png',
		// 	text: '保养月度总览',
		// 	badge: '2',
		// 	type: "error"
		// },
		// {
		// 	id: 5,
		// 	url: '/static/images/appICON.png',
		// 	text: '保养详细统计'
		// },
		// {
		// 	id: 6,
		// 	url: '/static/images/appICON.png',
		// 	text: 'Grid 6'
		// },
		// {
		// 	id: 7,
		// 	url: '/static/images/appICON.png',
		// 	text: 'Grid 7'
		// },
		// {
		// 	id: 8,
		// 	url: '/static/images/appICON.png',
		// 	text: 'Grid 8'
		// },
		// {
		// 	id: 9,
		// 	url: '/static/images/appICON.png',
		// 	text: 'Grid 9'
		// }

	])

onMounted(() => {
	checkMenuDataBeforeLoad()
})

// 下拉刷新处理函数
onPullDownRefresh(async () => {
	console.log('下拉刷新处理函数');
	try {
		// 重新加载数据
		loadFavouriteList()

		// 停止刷新动画（必须调用）
		setTimeout(function () {
			uni.stopPullDownRefresh({
				// success: function () {
				// 	uni.showToast({
				// 		title:'刷新成功！'
				// 	})
				// },
				// fail: function () {
				// 	uni.showToast({
				// 		title:'刷新失败！'
				// 	})
				// },
				// complete: function () {
				// 	uni.showToast({
				// 		title:'刷新完成！'
				// 	})
				// }
			});
		}, 1000);
	} catch (error) {
		console.error('刷新失败:', error);
		setTimeout(function () {
			// 失败时也要停止动画
			uni.stopPullDownRefresh();
		}, 1000);
	}
});
//////////////////////methods//////////////////////////
// 保留当前页面，跳转到应用内的某个页面，使用uni.navigateBack可以返回到原页面。
function navigateTo(url, params) {
	console.log("navigateTo===params===", JSON.stringify(params))
	if(!params.text){
		params.text = params.CMODULE_NAME
	}
	//在起始页面跳转到test.vue页面并传递参数
	uni.navigateTo({
		url: url,//'test?id=1&name=uniapp'
		success: function (res) {
			// 通过eventChannel向被打开页面传送数据
			res.eventChannel.emit('acceptDataFromOpenerPage', params)
		}
	});
}
function checkMenuDataBeforeLoad() {
	let _ModuleData = getStorageSync('Favourite')
	if (_ModuleData) {
		handleMenuData(_ModuleData)
	} else {
		loadFavouriteList()
	}
}
// 正式数据查询过滤为Work
function handleMenuData(menuDataList = []) {
	let _menuModulesData = menuDataList //.ModuleTrees.filter(item=>item.CBEHAVIOR_PATH == 'App')//Work
	if (_menuModulesData && _menuModulesData.length > 0) {
		menuCategory.value = _menuModulesData//[0].Children
		list.value = JSON.parse(JSON.stringify(menuCategory.value))
		copyList.value = JSON.parse(JSON.stringify(list.value))
	}
}
function savefavouriteSeq() {

	serviceApi.favouriteSeq(copyList.value).then(res => {
		//debugger
		if (res && res.data.code === 200 && res.data.data.Success) {
			uni.showToast({
				title: '保存成功',
				icon: 'none'
			})

		}
	}).catch(err => {
		uni.showToast({
			title: '网络异常，请重试',
			icon: 'none'
		})
	}).finally(() => {
		uni.hideLoading();
	})
}
// 加载 收藏/工作台-菜单数据 
function loadFavouriteList() {
	const params = {
		username: getCurrentUserInfoByField('CUSER_NAME'),
		systemtype: 'APP'
	}
	uni.showLoading({
		title: '加载中'
	});
	serviceApi.favourite(null, params).then(res => {
		//debugger
		if (res && res.data.code === 200 && res.data.data.Success) {

			setStorageSync('Favourite', res.data.data.Datas)
			handleMenuData(res.data.data.Datas)

		} else {
			uni.showToast({
				title: res && res.data.data.Content ? res.data.data.Content : '获取菜单信息失败',
				icon: 'none'
			})
		}
	}).catch(err => {
		uni.showToast({
			title: '网络异常，请重试',
			icon: 'none'
		})
	}).finally(() => {
		console.log("finally")
		uni.hideLoading();
	})
}

function toggleMenu(type) {
	uni.showActionSheet({
		itemList: ['编辑删除', '取消编辑'],
		success: function (res) {
			console.log('选中了第' + (res.tapIndex + 1) + '个按钮');
			console.log('list.value:', list.value)
			//savefavouriteSeq()
			let tabItemIndex = res.tapIndex + 1
			if (tabItemIndex == 1) {
				isEdit.value = true
				//isOpenAllCollapse.value = true
			} else {
				isEdit.value = false
			}
		},
		fail: function (res) {
			console.log(res.errMsg);
		}
	});
}
function removeItem(item) {
	if(item.CID){
		removeItemInDB(item.CID)
		list.value = list.value.filter(subItem => {
			return subItem.CID != item.CID
		})
	}
}

function removeItemInDB(CID){
	let params ={
		CID:CID
	}
	serviceApi.favouriteDelete(params).then(res => {
		//debugger
		if (res && res.data.code === 200 && res.data.data.Success) {
			uni.showToast({
				title: '删除成功',
				icon: 'none'
			})
	
		}
	}).catch(err => {
		uni.showToast({
			title: '删除失败',
			icon: 'none'
		})
	}).finally(() => {
		
	})
}
// 排序回调
const setList = (l,sourceTargetItems) => {
	console.log('排序回调:', l)
	list.value = l
	copyList.value = JSON.parse(JSON.stringify(list.value))
	//getSourceTargetItems(l)
}
function getSourceTargetItems(list=[]){
	if(list.length>0){
		// 筛选出发生位置变化的项目
		const changedItems = list.filter(item => item.index !== item.sortIndex);
		
		// 检查是否存在两个项目交换位置的情况
		if (changedItems.length === 2) {
		  // 判断哪一个是源项目，哪一个是目标项目
		  const sourceItem = changedItems[0].sortIndex === changedItems[1].index 
		    ? changedItems[0] 
		    : changedItems[1];
		  
		  const targetItem = sourceItem === changedItems[0] 
		    ? changedItems[1] 
		    : changedItems[0];
		
		  console.log("源项目 CID:", sourceItem.CMODULE_NAME);
		  console.log("目标项目 CID:", targetItem.CMODULE_NAME);
		} else {
		  console.log("未找到符合条件的交换项目");
		}
	}
}

// 应用搜索
function searchAppClick() {
	let val = searchInput.value
	if (val) {
		list.value = copyList.value.filter(item => item.CMODULE_NAME.includes(val))
	} else {
		list.value = JSON.parse(JSON.stringify(copyList.value))
	}
}
</script>


<style lang="scss">
.imageItem {
	width: 60rpx;
	height: 60rpx;
}

.textDesc {
	font-size: 24rpx;
	margin-top: 10rpx;
	color: #363636;
}
</style>